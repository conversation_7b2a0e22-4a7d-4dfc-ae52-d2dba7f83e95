package com.gy.show.controller.external;

import com.gy.show.common.Result;
import com.gy.show.service.ExternalDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 外部数据监控控制器
 * 用于监控和管理数据转发状态
 */
@Slf4j
@Api(tags = "外部数据监控")
@RestController
@RequestMapping("/external/monitor")
public class ExternalDataMonitorController {

    @Autowired
    private ExternalDataService externalDataService;

    @ApiOperation("获取数据转发缓存状态")
    @GetMapping("/ship/cache/status")
    public Result getShipDataCacheStatus() {
        try {
            Map<String, Object> status = externalDataService.getShipDataCacheStatus();
            return Result.ok(status);
        } catch (Exception e) {
            log.error("获取数据转发缓存状态失败", e);
            return Result.error("获取数据转发缓存状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("清空数据转发缓存")
    @PostMapping("/ship/cache/clear")
    public Result clearShipDataCache() {
        try {
            externalDataService.clearShipDataCache();
            return Result.ok("数据转发缓存已清空");
        } catch (Exception e) {
            log.error("清空数据转发缓存失败", e);
            return Result.error("清空数据转发缓存失败: " + e.getMessage());
        }
    }
}
