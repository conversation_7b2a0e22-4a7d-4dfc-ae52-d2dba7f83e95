package com.gy.show.controller.external;

import com.gy.show.common.Result;
import com.gy.show.service.ExternalDataConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部数据配置管理控制器
 */
@Slf4j
@Api(tags = "外部数据配置管理")
@RestController
@RequestMapping("/external/config")
public class ExternalDataConfigController {

    @Autowired
    private ExternalDataConfigService externalDataConfigService;

    @ApiOperation("获取数据跟随状态")
    @GetMapping("/follow/status")
    public Result getFollowStatus() {
        try {
            boolean status = externalDataConfigService.getFollowStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", status);
            result.put("statusText", status ? "开启" : "关闭");
            return Result.ok(result);
        } catch (Exception e) {
            log.error("获取数据跟随状态失败", e);
            return Result.error("获取数据跟随状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("设置数据跟随状态")
    @PostMapping("/follow/status")
    public Result setFollowStatus(@ApiParam(value = "跟随状态，true表示开启，false表示关闭", required = true) 
                                  @RequestParam("status") Boolean status) {
        try {
            if (status == null) {
                return Result.error("状态参数不能为空");
            }
            
            externalDataConfigService.setFollowStatus(status);
            
            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", status);
            result.put("statusText", status ? "开启" : "关闭");
            result.put("message", "数据跟随状态已设置为: " + (status ? "开启" : "关闭"));
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("设置数据跟随状态失败", e);
            return Result.error("设置数据跟随状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("切换数据跟随状态")
    @PostMapping("/follow/toggle")
    public Result toggleFollowStatus() {
        try {
            boolean newStatus = externalDataConfigService.toggleFollowStatus();
            
            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", newStatus);
            result.put("statusText", newStatus ? "开启" : "关闭");
            result.put("message", "数据跟随状态已切换为: " + (newStatus ? "开启" : "关闭"));
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("切换数据跟随状态失败", e);
            return Result.error("切换数据跟随状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("开启数据跟随")
    @PostMapping("/follow/enable")
    public Result enableFollow() {
        try {
            externalDataConfigService.setFollowStatus(true);
            
            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", true);
            result.put("statusText", "开启");
            result.put("message", "数据跟随已开启");
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("开启数据跟随失败", e);
            return Result.error("开启数据跟随失败: " + e.getMessage());
        }
    }

    @ApiOperation("关闭数据跟随")
    @PostMapping("/follow/disable")
    public Result disableFollow() {
        try {
            externalDataConfigService.setFollowStatus(false);
            
            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", false);
            result.put("statusText", "关闭");
            result.put("message", "数据跟随已关闭");
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("关闭数据跟随失败", e);
            return Result.error("关闭数据跟随失败: " + e.getMessage());
        }
    }
}
