package com.gy.show.service;

import com.gy.show.entity.dto.ConfirmScheduleDTO;
import com.gy.show.enums.ExternalDataTypeEnum;
import com.gy.show.socket.message.HeadMessage;

import java.awt.geom.Point2D;
import java.util.List;

public interface ExternalDataService {

    void handlerBusinessData(Object msg, String id, String ter);

    void handlerRequirementData(Object msg, ExternalDataTypeEnum dataTypeEnum, String id);

    HeadMessage parseControlMessageHead(Object msg);

    void handlerTargetData(Object msg, ExternalDataTypeEnum dataTypeEnum, String id);

    void handlerSituationData(Object msg, ExternalDataTypeEnum dataTypeEnum, String ter);

    void packageScheduleResult(List<ConfirmScheduleDTO> confirmScheduleDTOs);

    void handlerResponseScheduleResult(Object msg);

    void handlerControlData(Object msg, ExternalDataTypeEnum carData, String id, String ter);

    void sendBusinessData(byte[] data, String dataType);

    void simulatePushBzData();

    void send2Ship(Point2D.Double point);
}
