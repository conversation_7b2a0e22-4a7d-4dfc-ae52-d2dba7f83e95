package com.gy.show.service;

/**
 * 外部数据配置管理服务
 * 用于管理外部数据处理相关的配置参数
 */
public interface ExternalDataConfigService {

    /**
     * 获取数据跟随状态
     * @return true表示开启跟随，false表示关闭跟随
     */
    boolean getFollowStatus();

    /**
     * 设置数据跟随状态
     * @param followStatus true表示开启跟随，false表示关闭跟随
     */
    void setFollowStatus(boolean followStatus);

    /**
     * 切换数据跟随状态
     * @return 切换后的状态
     */
    boolean toggleFollowStatus();
}
