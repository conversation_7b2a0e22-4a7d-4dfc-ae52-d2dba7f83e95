package com.gy.show.service.impl;

import com.gy.show.service.ExternalDataConfigService;
import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 外部数据配置管理服务实现
 */
@Slf4j
@Service
public class ExternalDataConfigServiceImpl implements ExternalDataConfigService {

    /**
     * Redis缓存key，用于持久化跟随状态
     */
    private static final String FOLLOW_STATUS_CACHE_KEY = "external:data:follow:status";

    /**
     * 内存中的跟随状态，用于快速访问
     */
    private final AtomicBoolean followStatus = new AtomicBoolean(false);

    /**
     * 初始化时从Redis加载状态
     */
    public ExternalDataConfigServiceImpl() {
        loadFollowStatusFromCache();
    }

    @Override
    public boolean getFollowStatus() {
        return followStatus.get();
    }

    @Override
    public void setFollowStatus(boolean followStatus) {
        this.followStatus.set(followStatus);
        // 同步到Redis缓存
        saveFollowStatusToCache(followStatus);
        log.info("数据跟随状态已设置为: {}", followStatus ? "开启" : "关闭");
    }

    @Override
    public boolean toggleFollowStatus() {
        boolean newStatus = !followStatus.get();
        setFollowStatus(newStatus);
        log.info("数据跟随状态已切换为: {}", newStatus ? "开启" : "关闭");
        return newStatus;
    }

    /**
     * 从Redis缓存加载跟随状态
     */
    private void loadFollowStatusFromCache() {
        try {
            String cachedStatus = RedisUtil.StringOps.get(FOLLOW_STATUS_CACHE_KEY);
            if (StringUtils.isNotBlank(cachedStatus)) {
                boolean status = Boolean.parseBoolean(cachedStatus);
                followStatus.set(status);
                log.info("从缓存加载数据跟随状态: {}", status ? "开启" : "关闭");
            } else {
                // 默认状态为关闭
                followStatus.set(false);
                saveFollowStatusToCache(false);
                log.info("初始化数据跟随状态为: 关闭");
            }
        } catch (Exception e) {
            log.error("从缓存加载数据跟随状态失败，使用默认状态: 关闭", e);
            followStatus.set(false);
        }
    }

    /**
     * 保存跟随状态到Redis缓存
     */
    private void saveFollowStatusToCache(boolean status) {
        try {
            RedisUtil.StringOps.set(FOLLOW_STATUS_CACHE_KEY, String.valueOf(status));
        } catch (Exception e) {
            log.error("保存数据跟随状态到缓存失败", e);
        }
    }
}
